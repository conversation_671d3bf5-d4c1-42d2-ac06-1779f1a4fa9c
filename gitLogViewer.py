import re
import subprocess
import datetime
import sys
import os

# 配置需要查询的作者
AUTHORS = ["daiqixiang", "zhanghongxiang", "王志磊", "韩兴宇", "wuxibin", "zheng<PERSON><PERSON>", "wangtianyu"]  # 替换为你需要的作者名字
# AUTHORS = []

SINCE_HOUR = 0  # 只保留N点到24点的提交记录

def get_git_log(date, date2, authors, repo_path):
    if len(authors) == 0:
        return get_git_log_imp(date, date2, None, repo_path)
    else:
        logs = []
        for author in authors:
            logs.append(get_git_log_imp(date, date2, author, repo_path))
        return "\n".join(logs)
            

def get_git_log_imp(date, date2, author, repo_path):
    logs = []
    # 帮我改下指令，我希望得到所有分支的提交记录
    cmd = [
        "git", "log",
        "--all",  # 新增参数，获取所有分支
        "--decorate",  # 显示分支和标签
        f'--since={date} 00:00:00',
        f'--until={date2} 23:59:59',
        '--pretty=format:%h %an %ad %d %s',
        '--date=iso',
        '--no-merges'
    ]
    if author is not None:
        cmd.append(f'--author={author}')


    result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding="utf-8", cwd=repo_path)
    if result.stdout:
        # 屏蔽掉 'merge branch' 的行, 并且替换 ' +0800' 为 ''
        # 同时筛选出晚上 19点到 24点的提交记录
        filtered_lines = []
        for line in result.stdout.splitlines():
            if SINCE_HOUR == 0:
                filtered_lines.append(line.replace(" +0800", ""))
            else:
                if "Merge branch" not in line:
                    # 只保留晚上 19点到 24点的提交记录
                    try:
                        #使用正则匹配日期
                        commite_date = re.search(r' \d{4}-\d{2}-\d{2} (\d{2}):\d{2}:\d{2} ', line)
                        if not commite_date:
                            continue
                        hour = int(commite_date.group(1))
                        if hour >= 19:  # 只保留晚上19点到24点的提交记录
                            filtered_lines.append(line.replace(" +0800", ""))
                    except ValueError:
                        continue
        if filtered_lines:
            # 将筛选后的提交记录按日期(不需要时间)分组
            logs.append(f"Commits by {author} on {date} {len(filtered_lines)}条记录:\n" + "\n".join(filtered_lines) + "\n")
    return "\n".join(logs)

def main():
    # 参数调换，第一个参数是git文件夹，第二个参数是日期
    if len(sys.argv) > 1:
        repo_path = sys.argv[1]
    else:
        repo_path = "G:\MG\project-diy"

    if len(sys.argv) > 2:
        date = sys.argv[2]
    else:
        date = datetime.date.today().strftime("%Y-%m-%d")
        # date = "60"  # 默认查询过去14天的提交记录

    # 如果date是一个数字的话，表示查询过去几天的提交记录
    if date.isdigit():
        days_ago = int(date)
        date = (datetime.date.today() - datetime.timedelta(days=days_ago)).strftime("%Y-%m-%d")
        date2 = datetime.date.today().strftime("%Y-%m-%d")
        filename = f"git_log_{date}_to_{date2}.txt"
    else:
        try:
            filename = f"git_log_{date}.txt"
            date = datetime.datetime.strptime(date, "%Y-%m-%d")
            date2 = date # 默认查询当天的提交记录
        except ValueError:
            print("日期格式错误，请使用 YYYY-MM-DD 格式。")
            sys.exit(2)

    log_text = get_git_log(date, date2, AUTHORS, repo_path)
    if log_text:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(log_text)
        print(f"日志已保存到 {filename}")
    else:
        print("没有找到提交记录。")

if __name__ == "__main__":
    main()